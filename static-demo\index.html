<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 3D Marketplace - Успішно розгорнуто на Cloudflare Pages!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature h3 {
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .stat {
            text-align: center;
            margin: 0.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4ade80;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .buttons {
            margin-top: 2rem;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            border: none;
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(34, 197, 94, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #22c55e;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1>3D Marketplace</h1>
        <p class="subtitle">Успішно розгорнуто на Cloudflare Pages!</p>
        
        <div class="status">
            <span class="status-indicator"></span>
            <strong>Статус:</strong> Всі системи працюють
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🗄️</div>
                <h3>D1 Database</h3>
                <p>Cloudflare D1 з повною схемою та 41 таблицею</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🗂️</div>
                <h3>KV Storage</h3>
                <p>Швидке кешування з KV namespaces</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📦</div>
                <h3>R2 Storage</h3>
                <p>Зберігання файлів моделей в R2 buckets</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>Analytics</h3>
                <p>Real-time моніторинг та метрики</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🔧</div>
                <h3>API Ready</h3>
                <p>Повний API для скрапінгу та імпорту</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🚨</div>
                <h3>Monitoring</h3>
                <p>Система алертів та health checks</p>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-number">5</div>
                <div class="stat-label">Платформ</div>
            </div>
            <div class="stat">
                <div class="stat-number">50+</div>
                <div class="stat-label">Популярних моделей</div>
            </div>
            <div class="stat">
                <div class="stat-number">165K+</div>
                <div class="stat-label">Завантажень</div>
            </div>
            <div class="stat">
                <div class="stat-number">96%</div>
                <div class="stat-label">Здоров'я системи</div>
            </div>
        </div>
        
        <div class="buttons">
            <a href="/api/test-import" class="btn btn-primary">🔧 Тестувати API</a>
            <a href="/admin/import-demo" class="btn">📊 Дашборд</a>
            <a href="/api/monitoring/metrics" class="btn">📈 Метрики</a>
        </div>
        
        <div class="footer">
            <p>🌐 Розгорнуто на Cloudflare Pages з повною інтеграцією D1, KV, R2 та Analytics Engine</p>
            <p>⚡ Готово до масштабування та production використання</p>
        </div>
    </div>
    
    <script>
        // Простий health check
        fetch('/api/test-import')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ API працює:', data);
                }
            })
            .catch(error => {
                console.log('⚠️ API ще не готовий:', error);
            });
            
        // Анімація статистики
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(num => {
                const target = parseInt(num.textContent.replace(/[^\d]/g, ''));
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    num.textContent = num.textContent.includes('K') ? 
                        Math.floor(current) + 'K+' : 
                        num.textContent.includes('%') ?
                        Math.floor(current) + '%' :
                        Math.floor(current) + (num.textContent.includes('+') ? '+' : '');
                }, 50);
            });
        }
        
        // Запуск анімації після завантаження
        setTimeout(animateNumbers, 500);
    </script>
</body>
</html>
