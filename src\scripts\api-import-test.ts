#!/usr/bin/env tsx

/**
 * Тестування імпорту через API
 */

async function testAPIImport() {
  console.log('🌐 Тестування імпорту через API');
  console.log('=' .repeat(50));

  const testUrls = [
    'https://www.thingiverse.com/thing:3495390',
    'https://www.myminifactory.com/object/3d-print-articulated-dragon-mcgybeer-738'
  ];

  // Тест валідації URL
  console.log('\n🔍 Тестування валідації URL...');
  for (const url of testUrls) {
    try {
      const response = await fetch(`http://localhost:3000/api/scraping/single-import/validate?url=${encodeURIComponent(url)}`);
      const result = await response.json();
      
      console.log(`📋 ${url}:`);
      console.log(`  Platform: ${result.data?.platform}`);
      console.log(`  Supported: ${result.data?.supported}`);
      console.log(`  Can Import: ${result.data?.canImport}`);
      console.log(`  Estimated Time: ${result.data?.estimatedTime}s`);
      
      if (result.data?.warnings?.length > 0) {
        console.log(`  Warnings: ${result.data.warnings.join(', ')}`);
      }
    } catch (error) {
      console.error(`❌ Помилка валідації ${url}:`, error);
    }
  }

  // Тест одиночного імпорту
  console.log('\n📥 Тестування одиночного імпорту...');
  for (const url of testUrls) {
    try {
      const response = await fetch('http://localhost:3000/api/scraping/single-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          url,
          options: { saveToDatabase: false } // Не зберігаємо в БД для тесту
        })
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(`✅ Успішний імпорт з ${result.data.platform}:`);
        console.log(`  Назва: ${result.data.model.name}`);
        console.log(`  Автор: ${result.data.model.author_name}`);
        console.log(`  Категорія: ${result.data.model.category}`);
        console.log(`  Безкоштовно: ${result.data.model.is_free}`);
        console.log(`  Формати файлів: ${result.data.model.file_formats?.join(', ')}`);
      } else {
        console.error(`❌ Помилка імпорту ${url}:`, result.error.message);
      }
    } catch (error) {
      console.error(`💥 Критична помилка ${url}:`, error);
    }
  }

  // Тест пакетного імпорту
  console.log('\n📦 Тестування пакетного імпорту...');
  try {
    const response = await fetch('http://localhost:3000/api/scraping/batch-import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        urls: testUrls.slice(0, 2), // Тільки 2 URL для тесту
        options: {
          parallel: 1,
          retryFailed: true,
          includeFiles: true,
          includeImages: true
        }
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ Пакетне завдання створено:`);
      console.log(`  Job ID: ${result.data.jobId}`);
      console.log(`  URL Count: ${result.data.urlCount}`);
      console.log(`  Estimated Duration: ${result.data.estimatedDuration}s`);
      
      // Моніторинг прогресу
      console.log('\n📊 Моніторинг прогресу...');
      await monitorBatchJob(result.data.jobId);
    } else {
      console.error('❌ Помилка створення пакетного завдання:', result.error.message);
    }
  } catch (error) {
    console.error('💥 Критична помилка пакетного імпорту:', error);
  }

  console.log('\n🎉 API тестування завершено!');
}

async function monitorBatchJob(jobId: string) {
  const maxAttempts = 30; // 30 спроб по 5 секунд = 2.5 хвилини
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`http://localhost:3000/api/scraping/batch-import?jobId=${jobId}`);
      const result = await response.json();

      if (result.success) {
        const job = result.data;
        console.log(`📈 Прогрес: ${job.progress.current}/${job.progress.total} (${job.progress.percentage}%) - ${job.progress.message}`);

        if (job.status === 'completed') {
          console.log('✅ Завдання завершено успішно!');
          if (job.result?.data?.summary) {
            const summary = job.result.data.summary;
            console.log(`📊 Підсумок: ${summary.successful} успішно, ${summary.failed} помилок з ${summary.total} загалом`);
          }
          break;
        } else if (job.status === 'failed') {
          console.log('❌ Завдання завершилося з помилкою');
          break;
        }
      } else {
        console.error('❌ Помилка отримання статусу завдання:', result.error.message);
        break;
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 5000)); // Чекаємо 5 секунд
    } catch (error) {
      console.error('💥 Помилка моніторингу:', error);
      break;
    }
  }

  if (attempts >= maxAttempts) {
    console.log('⏰ Тайм-аут моніторингу завдання');
  }
}

// Запуск тесту
if (require.main === module) {
  testAPIImport().catch(console.error);
}
