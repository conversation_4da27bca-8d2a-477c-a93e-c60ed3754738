import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// GET handler for recommended models
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const excludeModelId = searchParams.get('exclude');
    const userId = searchParams.get('userId');
    const personalized = searchParams.get('personalized') === 'true';
    const limit = Number(searchParams.get('limit')) || 8;

    let sql = `
      SELECT 
        m.id,
        m.name,
        m.description,
        m.thumbnail_url,
        m.price,
        m.is_free,
        m.category,
        m.download_count,
        m.like_count,
        m.view_count,
        m.created_at,
        u.name as author_name,
        u.avatar_url as author_avatar,
        m.user_id
      FROM models m
      JOIN users u ON m.user_id = u.id
      WHERE 1=1
    `;

    const queryParams: any[] = [];

    // Виключаємо конкретну модель (наприклад, поточну)
    if (excludeModelId) {
      sql += ` AND m.id != ?`;
      queryParams.push(excludeModelId);
    }

    // Персоналізовані рекомендації
    if (personalized && userId) {
      // Рекомендації на основі історії користувача
      sql = `
        WITH user_categories AS (
          SELECT m.category, COUNT(*) as category_count
          FROM user_models um
          JOIN models m ON um.model_id = m.id
          WHERE um.user_id = ?
          GROUP BY m.category
          ORDER BY category_count DESC
          LIMIT 3
        ),
        user_tags AS (
          SELECT TRIM(tag.value) as tag, COUNT(*) as tag_count
          FROM user_models um
          JOIN models m ON um.model_id = m.id,
          json_each('["' || REPLACE(REPLACE(m.tags, ',', '","'), ' ', '') || '"]') as tag
          WHERE um.user_id = ? AND m.tags IS NOT NULL AND m.tags != ''
          GROUP BY tag.value
          ORDER BY tag_count DESC
          LIMIT 5
        )
        SELECT DISTINCT
          m.id,
          m.name,
          m.description,
          m.thumbnail_url,
          m.price,
          m.is_free,
          m.category,
          m.download_count,
          m.like_count,
          m.view_count,
          m.created_at,
          u.name as author_name,
          u.avatar_url as author_avatar,
          m.user_id,
          (
            CASE WHEN m.category IN (SELECT category FROM user_categories) THEN 3 ELSE 0 END +
            CASE WHEN m.download_count > 50 THEN 2 ELSE 0 END +
            CASE WHEN m.like_count > 10 THEN 1 ELSE 0 END
          ) as relevance_score
        FROM models m
        JOIN users u ON m.user_id = u.id
        WHERE m.user_id != ?
      `;
      
      queryParams.push(userId, userId, userId);
      
      if (excludeModelId) {
        sql += ` AND m.id != ?`;
        queryParams.push(excludeModelId);
      }
      
      sql += ` ORDER BY relevance_score DESC, m.download_count DESC`;
    } else {
      // Загальні рекомендації
      if (category) {
        sql += ` AND m.category = ?`;
        queryParams.push(category);
      }

      // Сортування за популярністю та новизною
      sql += ` ORDER BY 
        (m.download_count * 0.4 + m.like_count * 0.3 + m.view_count * 0.2 + 
         CASE WHEN m.created_at > datetime('now', '-30 days') THEN 10 ELSE 0 END) DESC,
        m.created_at DESC
      `;
    }

    sql += ` LIMIT ?`;
    queryParams.push(limit);

    const models = await query(sql, queryParams);

    // Якщо персоналізованих рекомендацій недостатньо, додаємо популярні моделі
    if (personalized && models.length < limit) {
      const additionalSql = `
        SELECT 
          m.id,
          m.name,
          m.description,
          m.thumbnail_url,
          m.price,
          m.is_free,
          m.category,
          m.download_count,
          m.like_count,
          m.view_count,
          m.created_at,
          u.name as author_name,
          u.avatar_url as author_avatar,
          m.user_id
        FROM models m
        JOIN users u ON m.user_id = u.id
        WHERE m.user_id != ?
        ${excludeModelId ? 'AND m.id != ?' : ''}
        AND m.id NOT IN (${models.map(() => '?').join(',') || 'NULL'})
        ORDER BY m.download_count DESC, m.like_count DESC
        LIMIT ?
      `;

      const additionalParams = [userId];
      if (excludeModelId) additionalParams.push(excludeModelId);
      additionalParams.push(...models.map((m: any) => m.id));
      additionalParams.push(limit - models.length);

      const additionalModels = await query(additionalSql, additionalParams);
      models.push(...additionalModels);
    }

    // Обробка результатів
    const processedModels = models.map((model: any) => ({
      ...model,
      rating: 4.0 + Math.random() * 1.0, // Тимчасовий рейтинг
    }));

    return NextResponse.json({
      success: true,
      data: processedModels,
      total: processedModels.length
    });

  } catch (error) {
    console.error('Error fetching recommended models:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch recommended models' },
      { status: 500 }
    );
  }
}
