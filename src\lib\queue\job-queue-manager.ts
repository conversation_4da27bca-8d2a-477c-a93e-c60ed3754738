/**
 * Job Queue Manager for handling large import operations
 * Provides background processing, progress tracking, and retry logic
 */

import { ModelSource } from '@/types/models';
import { EnhancedError, ErrorCategory, ErrorSeverity, errorHandler } from '@/lib/error-handling/enhanced-error-handler';

export enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  RETRYING = 'retrying'
}

export enum JobPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4
}

export interface JobProgress {
  current: number;
  total: number;
  percentage: number;
  message: string;
  estimatedTimeRemaining?: number;
}

export interface JobResult {
  success: boolean;
  data?: any;
  error?: string;
  details?: Record<string, any>;
}

export interface Job {
  id: string;
  type: string;
  data: any;
  status: JobStatus;
  priority: JobPriority;
  progress: JobProgress;
  result?: JobResult;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  retryCount: number;
  maxRetries: number;
  retryDelay: number;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface BatchImportJobData {
  urls: string[];
  options: {
    parallel?: number;
    retryFailed?: boolean;
    includeFiles?: boolean;
    includeImages?: boolean;
    validateLicense?: boolean;
    autoPublish?: boolean;
  };
  userId?: string;
}

export interface JobQueueOptions {
  maxConcurrentJobs: number;
  defaultRetryDelay: number;
  defaultMaxRetries: number;
  progressUpdateInterval: number;
}

export class JobQueueManager {
  private static instance: JobQueueManager;
  private jobs: Map<string, Job> = new Map();
  private activeJobs: Set<string> = new Set();
  private options: JobQueueOptions;
  private progressUpdateCallbacks: Map<string, (progress: JobProgress) => void> = new Map();

  constructor(options: Partial<JobQueueOptions> = {}) {
    this.options = {
      maxConcurrentJobs: 3,
      defaultRetryDelay: 5000,
      defaultMaxRetries: 3,
      progressUpdateInterval: 1000,
      ...options
    };
  }

  static getInstance(options?: Partial<JobQueueOptions>): JobQueueManager {
    if (!JobQueueManager.instance) {
      JobQueueManager.instance = new JobQueueManager(options);
    }
    return JobQueueManager.instance;
  }

  /**
   * Add a new job to the queue
   */
  async addJob(
    type: string,
    data: any,
    options: {
      priority?: JobPriority;
      maxRetries?: number;
      retryDelay?: number;
      userId?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<string> {
    const jobId = this.generateJobId();
    const job: Job = {
      id: jobId,
      type,
      data,
      status: JobStatus.PENDING,
      priority: options.priority || JobPriority.NORMAL,
      progress: {
        current: 0,
        total: 1,
        percentage: 0,
        message: 'Завдання додано до черги'
      },
      createdAt: new Date(),
      retryCount: 0,
      maxRetries: options.maxRetries || this.options.defaultMaxRetries,
      retryDelay: options.retryDelay || this.options.defaultRetryDelay,
      userId: options.userId,
      metadata: options.metadata
    };

    this.jobs.set(jobId, job);
    
    // Start processing if there's capacity
    this.processNextJob();
    
    return jobId;
  }

  /**
   * Add a batch import job
   */
  async addBatchImportJob(
    urls: string[],
    options: BatchImportJobData['options'] = {},
    jobOptions: {
      priority?: JobPriority;
      userId?: string;
    } = {}
  ): Promise<string> {
    const jobData: BatchImportJobData = {
      urls,
      options: {
        parallel: 3,
        retryFailed: true,
        includeFiles: true,
        includeImages: true,
        validateLicense: true,
        autoPublish: false,
        ...options
      },
      userId: jobOptions.userId
    };

    return this.addJob('batch_import', jobData, {
      priority: jobOptions.priority || JobPriority.NORMAL,
      userId: jobOptions.userId,
      metadata: {
        urlCount: urls.length,
        estimatedDuration: urls.length * 5 // 5 seconds per URL estimate
      }
    });
  }

  /**
   * Get job status and progress
   */
  getJob(jobId: string): Job | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * Get all jobs for a user
   */
  getUserJobs(userId: string): Job[] {
    return Array.from(this.jobs.values()).filter(job => job.userId === userId);
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job) return false;

    if (job.status === JobStatus.PENDING) {
      job.status = JobStatus.CANCELLED;
      job.completedAt = new Date();
      return true;
    }

    if (job.status === JobStatus.PROCESSING) {
      job.status = JobStatus.CANCELLED;
      this.activeJobs.delete(jobId);
      job.completedAt = new Date();
      return true;
    }

    return false;
  }

  /**
   * Subscribe to job progress updates
   */
  subscribeToProgress(jobId: string, callback: (progress: JobProgress) => void): void {
    this.progressUpdateCallbacks.set(jobId, callback);
  }

  /**
   * Unsubscribe from job progress updates
   */
  unsubscribeFromProgress(jobId: string): void {
    this.progressUpdateCallbacks.delete(jobId);
  }

  /**
   * Process the next job in the queue
   */
  private async processNextJob(): Promise<void> {
    if (this.activeJobs.size >= this.options.maxConcurrentJobs) {
      return;
    }

    const nextJob = this.getNextPendingJob();
    if (!nextJob) {
      return;
    }

    this.activeJobs.add(nextJob.id);
    nextJob.status = JobStatus.PROCESSING;
    nextJob.startedAt = new Date();

    try {
      await this.executeJob(nextJob);
    } catch (error) {
      await this.handleJobError(nextJob, error as Error);
    } finally {
      this.activeJobs.delete(nextJob.id);
      // Process next job
      setTimeout(() => this.processNextJob(), 100);
    }
  }

  /**
   * Get the next pending job with highest priority
   */
  private getNextPendingJob(): Job | undefined {
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === JobStatus.PENDING)
      .sort((a, b) => {
        // Sort by priority (higher first), then by creation time (older first)
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return a.createdAt.getTime() - b.createdAt.getTime();
      });

    return pendingJobs[0];
  }

  /**
   * Execute a specific job
   */
  private async executeJob(job: Job): Promise<void> {
    this.updateProgress(job.id, {
      current: 0,
      total: 1,
      percentage: 0,
      message: 'Розпочато виконання завдання'
    });

    switch (job.type) {
      case 'batch_import':
        await this.executeBatchImportJob(job);
        break;
      case 'single_import':
        await this.executeSingleImportJob(job);
        break;
      default:
        throw new Error(`Unknown job type: ${job.type}`);
    }
  }

  /**
   * Execute batch import job
   */
  private async executeBatchImportJob(job: Job): Promise<void> {
    const data = job.data as BatchImportJobData;
    const { urls, options } = data;
    const parallel = options.parallel || 3;
    
    const results: Array<{ url: string; success: boolean; modelId?: string; error?: string }> = [];
    let completed = 0;

    this.updateProgress(job.id, {
      current: 0,
      total: urls.length,
      percentage: 0,
      message: `Імпорт ${urls.length} моделей`
    });

    // Process URLs in batches
    for (let i = 0; i < urls.length; i += parallel) {
      const batch = urls.slice(i, i + parallel);
      const batchPromises = batch.map(async (url) => {
        try {
          const result = await this.importSingleModel(url, options);
          completed++;
          
          this.updateProgress(job.id, {
            current: completed,
            total: urls.length,
            percentage: Math.round((completed / urls.length) * 100),
            message: `Імпортовано ${completed} з ${urls.length} моделей`
          });

          return { url, success: true, modelId: result.modelId };
        } catch (error) {
          completed++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          
          this.updateProgress(job.id, {
            current: completed,
            total: urls.length,
            percentage: Math.round((completed / urls.length) * 100),
            message: `Помилка імпорту: ${errorMessage}`
          });

          return { url, success: false, error: errorMessage };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches
      if (i + parallel < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    job.status = JobStatus.COMPLETED;
    job.completedAt = new Date();
    job.result = {
      success: true,
      data: {
        results,
        summary: {
          total: urls.length,
          successful: successCount,
          failed: failureCount
        }
      }
    };

    this.updateProgress(job.id, {
      current: urls.length,
      total: urls.length,
      percentage: 100,
      message: `Завершено: ${successCount} успішно, ${failureCount} помилок`
    });
  }

  /**
   * Execute single import job
   */
  private async executeSingleImportJob(job: Job): Promise<void> {
    const { url, options } = job.data;
    
    try {
      const result = await this.importSingleModel(url, options);
      
      job.status = JobStatus.COMPLETED;
      job.completedAt = new Date();
      job.result = {
        success: true,
        data: result
      };

      this.updateProgress(job.id, {
        current: 1,
        total: 1,
        percentage: 100,
        message: 'Модель успішно імпортована'
      });
    } catch (error) {
      throw error; // Will be handled by handleJobError
    }
  }

  /**
   * Import a single model (placeholder implementation)
   */
  private async importSingleModel(url: string, options: any): Promise<{ modelId: string }> {
    // This would call the actual scraping API
    const response = await fetch('/api/scraping/import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url, options })
    });

    if (!response.ok) {
      throw new Error(`Import failed: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error?.message || 'Import failed');
    }

    return { modelId: result.data.modelId };
  }

  /**
   * Handle job errors and retry logic
   */
  private async handleJobError(job: Job, error: Error): Promise<void> {
    const enhancedError = await errorHandler.handleError(error, {
      platform: job.metadata?.platform as ModelSource,
      userId: job.userId
    });

    if (job.retryCount < job.maxRetries && enhancedError.retryable) {
      job.retryCount++;
      job.status = JobStatus.RETRYING;
      
      this.updateProgress(job.id, {
        current: job.progress.current,
        total: job.progress.total,
        percentage: job.progress.percentage,
        message: `Повторна спроба ${job.retryCount}/${job.maxRetries}`
      });

      // Schedule retry
      setTimeout(() => {
        job.status = JobStatus.PENDING;
        this.processNextJob();
      }, job.retryDelay * Math.pow(2, job.retryCount - 1)); // Exponential backoff
    } else {
      job.status = JobStatus.FAILED;
      job.completedAt = new Date();
      job.result = {
        success: false,
        error: enhancedError.userMessage,
        details: {
          code: enhancedError.code,
          category: enhancedError.category,
          retryCount: job.retryCount
        }
      };

      this.updateProgress(job.id, {
        current: job.progress.current,
        total: job.progress.total,
        percentage: job.progress.percentage,
        message: `Помилка: ${enhancedError.userMessage}`
      });
    }
  }

  /**
   * Update job progress and notify subscribers
   */
  private updateProgress(jobId: string, progress: Partial<JobProgress>): void {
    const job = this.jobs.get(jobId);
    if (!job) return;

    job.progress = { ...job.progress, ...progress };
    
    const callback = this.progressUpdateCallbacks.get(jobId);
    if (callback) {
      callback(job.progress);
    }
  }

  /**
   * Generate unique job ID
   */
  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    const jobs = Array.from(this.jobs.values());
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === JobStatus.PENDING).length,
      processing: jobs.filter(j => j.status === JobStatus.PROCESSING).length,
      completed: jobs.filter(j => j.status === JobStatus.COMPLETED).length,
      failed: jobs.filter(j => j.status === JobStatus.FAILED).length,
      cancelled: jobs.filter(j => j.status === JobStatus.CANCELLED).length,
      activeJobs: this.activeJobs.size,
      maxConcurrentJobs: this.options.maxConcurrentJobs
    };
  }

  /**
   * Clean up old completed jobs
   */
  cleanupOldJobs(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    
    for (const [jobId, job] of this.jobs.entries()) {
      if (job.completedAt && job.completedAt < cutoff) {
        this.jobs.delete(jobId);
        this.progressUpdateCallbacks.delete(jobId);
      }
    }
  }
}

// Export singleton instance
export const jobQueueManager = JobQueueManager.getInstance();
