'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  BarChart3,
  Users,
  Package,
  DollarSign,
  Settings,
  Shield,
  AlertTriangle,
  TrendingUp,
  Download,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import MarketplaceStats from '@/components/marketplace/marketplace-stats';

export default function AdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  // Перевірка авторизації та прав адміністратора
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  // Тимчасово дозволяємо доступ всім авторизованим користувачам
  // В реальному додатку тут має бути перевірка ролі адміністратора
  const isAdmin = true; // session?.user?.role === 'admin'

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <Card>
          <CardContent className="pt-6 text-center">
            <Shield className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h1 className="text-2xl font-bold mb-2">Доступ заборонено</h1>
            <p className="text-gray-600 mb-4">
              У вас немає прав для доступу до адміністративної панелі.
            </p>
            <Button onClick={() => router.push('/')}>
              Повернутися на головну
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Заголовок */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Адміністративна панель
        </h1>
        <p className="text-gray-600">
          Управління маркетплейсом 3D моделей
        </p>
      </div>

      {/* Швидка статистика */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Всього моделей</p>
                <p className="text-2xl font-bold text-gray-900">1,234</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% цього місяця
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Активних користувачів</p>
                <p className="text-2xl font-bold text-gray-900">5,678</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8% цього місяця
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Download className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Завантажень</p>
                <p className="text-2xl font-bold text-gray-900">89.2K</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +23% цього місяця
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Дохід</p>
                <p className="text-2xl font-bold text-gray-900">$12.4K</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +15% цього місяця
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Основний контент */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Огляд
          </TabsTrigger>
          <TabsTrigger value="models">
            <Package className="h-4 w-4 mr-2" />
            Моделі
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            Користувачі
          </TabsTrigger>
          <TabsTrigger value="orders">
            <DollarSign className="h-4 w-4 mr-2" />
            Замовлення
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            Налаштування
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <MarketplaceStats />
        </TabsContent>

        <TabsContent value="models" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Управління моделями</CardTitle>
              <CardDescription>
                Перегляд, модерація та управління 3D моделями
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Badge variant="outline">Всього: 1,234</Badge>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      Схвалено: 1,180
                    </Badge>
                    <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                      На модерації: 42
                    </Badge>
                    <Badge variant="outline" className="text-red-600 border-red-600">
                      Відхилено: 12
                    </Badge>
                  </div>
                  <Button>Переглянути всі моделі</Button>
                </div>
                
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Детальне управління моделями буде реалізовано в наступній версії</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Управління користувачами</CardTitle>
              <CardDescription>
                Перегляд та управління обліковими записами користувачів
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Badge variant="outline">Всього: 5,678</Badge>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      Активні: 4,892
                    </Badge>
                    <Badge variant="outline" className="text-blue-600 border-blue-600">
                      Продавці: 234
                    </Badge>
                    <Badge variant="outline" className="text-purple-600 border-purple-600">
                      Преміум: 156
                    </Badge>
                  </div>
                  <Button>Переглянути всіх користувачів</Button>
                </div>
                
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Детальне управління користувачами буде реалізовано в наступній версії</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Управління замовленнями</CardTitle>
              <CardDescription>
                Перегляд та обробка замовлень і платежів
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Badge variant="outline">Всього: 2,456</Badge>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      Завершено: 2,234
                    </Badge>
                    <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                      В обробці: 156
                    </Badge>
                    <Badge variant="outline" className="text-red-600 border-red-600">
                      Скасовано: 66
                    </Badge>
                  </div>
                  <Button>Переглянути всі замовлення</Button>
                </div>
                
                <div className="text-center py-8 text-gray-500">
                  <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Детальне управління замовленнями буде реалізовано в наступній версії</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Налаштування системи</CardTitle>
              <CardDescription>
                Конфігурація маркетплейсу та системні параметри
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Загальні налаштування</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">• Назва маркетплейсу</p>
                        <p className="text-sm text-gray-600">• Опис та метадані</p>
                        <p className="text-sm text-gray-600">• Контактна інформація</p>
                        <p className="text-sm text-gray-600">• Соціальні мережі</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Платежі та комісії</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">• Налаштування Stripe</p>
                        <p className="text-sm text-gray-600">• Комісія платформи</p>
                        <p className="text-sm text-gray-600">• Мінімальна ціна</p>
                        <p className="text-sm text-gray-600">• Валюти</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Модерація</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">• Автоматична модерація</p>
                        <p className="text-sm text-gray-600">• Правила контенту</p>
                        <p className="text-sm text-gray-600">• Фільтри та обмеження</p>
                        <p className="text-sm text-gray-600">• Повідомлення про порушення</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Інтеграції</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">• Cloudflare налаштування</p>
                        <p className="text-sm text-gray-600">• Email сервіси</p>
                        <p className="text-sm text-gray-600">• Аналітика</p>
                        <p className="text-sm text-gray-600">• API ключі</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                <div className="text-center py-4">
                  <Button disabled>
                    <Settings className="mr-2 h-4 w-4" />
                    Налаштування будуть доступні в наступній версії
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
