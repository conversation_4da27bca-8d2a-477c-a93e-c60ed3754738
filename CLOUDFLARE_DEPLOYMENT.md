# 🚀 Розгортання 3D Marketplace на Cloudflare Pages

## ⚡ Швидке розгортання (рекомендовано)

### 1. Підготовка
```bash
git clone <your-repo>
cd 3d-marketplace
npm install
```

### 2. Аутентифікація
```bash
npm install -g wrangler
wrangler login
```

### 3. Розгортання
```bash
npm run deploy:cloudflare
```

## 🔧 Повне розгортання з нуля

```bash
npm run deploy:full
```

## 🔐 Налаштування секретів

```bash
wrangler secret put NEXTAUTH_SECRET
wrangler secret put ADMIN_API_KEY
```

## 🌐 Результат

- **Сайт**: https://3d-marketplace.pages.dev
- **Дашборд**: https://3d-marketplace.pages.dev/admin/import-demo
- **API**: https://3d-marketplace.pages.dev/api/test-import

## 📊 Моніторинг

- **Метрики**: https://3d-marketplace.pages.dev/api/monitoring/metrics
- **Health Check**: https://3d-marketplace.pages.dev/api/health

## 🚨 Вирішення проблем

### Помилка аутентифікації:
```bash
wrangler logout && wrangler login
```

### Помилка збірки:
```bash
npm run clean && npm install && npm run build
```

### Перевірка ресурсів:
```bash
wrangler d1 list
wrangler kv:namespace list
wrangler r2 bucket list
```

---

🎉 **Готово! Ваш 3D Marketplace працює на Cloudflare Pages!**
