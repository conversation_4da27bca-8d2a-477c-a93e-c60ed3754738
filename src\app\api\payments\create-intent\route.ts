import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { queryOne, execute, generateId } from '@/lib/db';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// POST handler for creating payment intent
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { items, metadata = {} } = body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Items are required' },
        { status: 400 }
      );
    }

    // Calculate total amount
    let totalAmount = 0;
    const validatedItems = [];

    for (const item of items) {
      // Validate each item against database
      const model = await queryOne(
        'SELECT * FROM models WHERE id = ?',
        [item.id]
      );

      if (!model) {
        return NextResponse.json(
          { success: false, error: `Model ${item.id} not found` },
          { status: 404 }
        );
      }

      // Check if user already owns this model
      const existingAccess = await queryOne(
        'SELECT * FROM user_models WHERE user_id = ? AND model_id = ?',
        [session.user.id, item.id]
      );

      if (existingAccess) {
        return NextResponse.json(
          { success: false, error: `You already own model: ${(model as any).name}` },
          { status: 400 }
        );
      }

      // Check if user is trying to buy their own model
      if ((model as any).user_id === session.user.id) {
        return NextResponse.json(
          { success: false, error: `Cannot purchase your own model: ${(model as any).name}` },
          { status: 400 }
        );
      }

      const itemTotal = (model as any).price * item.quantity;
      totalAmount += itemTotal;

      validatedItems.push({
        id: item.id,
        name: (model as any).name,
        price: (model as any).price,
        quantity: item.quantity,
        total: itemTotal,
      });
    }

    // Convert to cents for Stripe
    const amountInCents = Math.round(totalAmount * 100);

    if (amountInCents < 50) { // Stripe minimum is $0.50
      return NextResponse.json(
        { success: false, error: 'Minimum order amount is $0.50' },
        { status: 400 }
      );
    }

    // Get or create Stripe customer
    let stripeCustomerId = null;
    const user = await queryOne(
      'SELECT stripe_customer_id FROM users WHERE id = ?',
      [session.user.id]
    );

    if ((user as any)?.stripe_customer_id) {
      stripeCustomerId = (user as any).stripe_customer_id;
    } else {
      // Create new Stripe customer
      const customer = await stripe.customers.create({
        email: session.user.email!,
        name: session.user.name || undefined,
        metadata: {
          userId: session.user.id,
        },
      });

      stripeCustomerId = customer.id;

      // Save customer ID to database
      await execute(
        'UPDATE users SET stripe_customer_id = ? WHERE id = ?',
        [stripeCustomerId, session.user.id]
      );
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: 'usd',
      customer: stripeCustomerId,
      metadata: {
        userId: session.user.id,
        itemCount: validatedItems.length.toString(),
        ...metadata,
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    // Create order record
    const orderId = generateId();
    await execute(
      `INSERT INTO orders (
        id, user_id, total_amount, status, payment_intent_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [orderId, session.user.id, totalAmount, 'pending', paymentIntent.id]
    );

    // Create order items
    for (const item of validatedItems) {
      const orderItemId = generateId();
      await execute(
        `INSERT INTO order_items (
          id, order_id, model_id, quantity, price, created_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
        [orderItemId, orderId, item.id, item.quantity, item.price]
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        orderId,
        amount: totalAmount,
        items: validatedItems,
      }
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}
