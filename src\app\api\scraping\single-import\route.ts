import { NextRequest, NextResponse } from 'next/server';
import { ScraperManager } from '@/lib/scrapers/scraper-manager';
import { errorHandler } from '@/lib/error-handling/enhanced-error-handler';
import { saveModelsToDatabase } from '@/lib/db';

/**
 * POST /api/scraping/single-import
 * Імпортує одну модель з URL
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, options = {}, userId } = body;

    // Валідація URL
    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'URL is required and must be a string',
            code: 'INVALID_URL'
          }
        },
        { status: 400 }
      );
    }

    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid URL format',
            code: 'MALFORMED_URL'
          }
        },
        { status: 400 }
      );
    }

    // Перевірка підтримуваних платформ
    const supportedPlatforms = [
      'thingiverse.com',
      'myminifactory.com',
      'printables.com',
      'thangs.com',
      'makerworld.com'
    ];

    const isSupported = supportedPlatforms.some(platform => 
      parsedUrl.hostname.includes(platform)
    );

    if (!isSupported) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: `Unsupported platform. Supported platforms: ${supportedPlatforms.join(', ')}`,
            code: 'UNSUPPORTED_PLATFORM',
            details: { supportedPlatforms }
          }
        },
        { status: 400 }
      );
    }

    // Імпорт моделі
    const scraperManager = new ScraperManager();
    const model = await scraperManager.importSingleModel(url);

    if (!model) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Failed to import model from the provided URL',
            code: 'IMPORT_FAILED'
          }
        },
        { status: 400 }
      );
    }

    // Збереження в базу даних (опціонально)
    if (options.saveToDatabase !== false) {
      try {
        await saveModelsToDatabase([model]);
      } catch (dbError) {
        console.error('Failed to save model to database:', dbError);
        // Не повертаємо помилку, оскільки модель була успішно імпортована
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        model: {
          id: model.id,
          name: model.name,
          description: model.description,
          thumbnail_url: model.thumbnail_url,
          category: model.category,
          tags: model.tags,
          author_name: model.author_name,
          download_count: model.download_count,
          like_count: model.like_count,
          is_free: model.is_free,
          price: model.price,
          file_formats: model.file_formats,
          source_platform: model.source_platform,
          source_url: model.source_url
        },
        platform: model.source_platform,
        importedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category,
          retryable: enhancedError.retryable
        }
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/scraping/single-import/validate?url=xxx
 * Валідує URL перед імпортом
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'URL parameter is required',
            code: 'MISSING_URL'
          }
        },
        { status: 400 }
      );
    }

    // Валідація URL
    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid URL format',
            code: 'MALFORMED_URL'
          }
        },
        { status: 400 }
      );
    }

    // Визначення платформи
    let platform = 'unknown';
    let supported = false;

    if (parsedUrl.hostname.includes('thingiverse.com')) {
      platform = 'thingiverse';
      supported = parsedUrl.pathname.includes('/thing:');
    } else if (parsedUrl.hostname.includes('myminifactory.com')) {
      platform = 'myminifactory';
      supported = parsedUrl.pathname.includes('/object/3d-print/');
    } else if (parsedUrl.hostname.includes('printables.com')) {
      platform = 'printables';
      supported = parsedUrl.pathname.includes('/model/');
    } else if (parsedUrl.hostname.includes('thangs.com')) {
      platform = 'thangs';
      supported = parsedUrl.pathname.includes('/designer/') || parsedUrl.pathname.includes('/m/');
    } else if (parsedUrl.hostname.includes('makerworld.com')) {
      platform = 'makerworld';
      supported = parsedUrl.pathname.includes('/models/');
    }

    // Додаткові перевірки для специфічних платформ
    let warnings: string[] = [];
    let estimatedTime = 5; // секунди

    if (platform === 'thingiverse' && !parsedUrl.pathname.match(/\/thing:\d+/)) {
      warnings.push('URL should contain a thing ID (e.g., /thing:123456)');
      supported = false;
    }

    if (platform === 'myminifactory' && !parsedUrl.pathname.includes('-')) {
      warnings.push('URL should contain a model slug with ID');
    }

    // Оцінка складності імпорту
    if (platform === 'thingiverse') {
      estimatedTime = 8; // Thingiverse може бути повільнішим
    } else if (platform === 'myminifactory') {
      estimatedTime = 6; // MyMiniFactory зазвичай швидший
    }

    return NextResponse.json({
      success: true,
      data: {
        url,
        platform,
        supported,
        warnings,
        estimatedTime,
        canImport: supported && warnings.length === 0,
        platformInfo: {
          name: platform,
          displayName: platform.charAt(0).toUpperCase() + platform.slice(1),
          features: {
            hasFiles: true,
            hasImages: true,
            hasStats: true,
            hasLicense: platform !== 'unknown',
            hasPricing: platform === 'myminifactory'
          }
        }
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}
