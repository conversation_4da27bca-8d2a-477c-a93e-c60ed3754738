/**
 * Base scraper class with common functionality for all platform scrapers
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import * as cheerio from 'cheerio';
import { ScrapedModel, ScrapedLicense, ModelSource, ScrapingError } from '@/types/models';

export interface ScraperConfig {
  userAgent: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  rateLimit: number; // requests per minute
}

export interface ScrapingContext {
  url: string;
  html?: string;
  $?: cheerio.CheerioAPI;
  data?: any;
  headers?: Record<string, string>;
}

export abstract class BaseScraper {
  protected platform: ModelSource;
  protected config: ScraperConfig;
  protected httpClient: AxiosInstance;

  constructor(platform: ModelSource, config: Partial<ScraperConfig> = {}) {
    this.platform = platform;
    this.config = {
      userAgent: `Mozilla/5.0 (compatible; 3DMarketplace-${platform}/1.0)`,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimit: 10,
      ...config,
    };

    this.httpClient = axios.create({
      timeout: this.config.timeout,
      headers: {
        'User-Agent': this.config.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });
  }

  /**
   * Main scraping method - to be implemented by each platform
   */
  abstract scrapeModel(url: string): Promise<ScrapedModel>;

  /**
   * Validate if URL belongs to this platform
   */
  abstract validateUrl(url: string): boolean;

  /**
   * Extract model ID from URL
   */
  abstract extractModelId(url: string): string | null;

  /**
   * Fetch HTML content with retry logic
   */
  protected async fetchHtml(url: string, options: AxiosRequestConfig = {}): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response = await this.httpClient.get(url, {
          ...options,
          validateStatus: (status) => status < 500, // Retry on 5xx errors
        });

        if (response.status === 404) {
          throw new ScrapingError('MODEL_NOT_FOUND', 'Model not found or URL is invalid', this.platform, url);
        }

        if (response.status === 403) {
          throw new ScrapingError('ACCESS_DENIED', 'Access denied - model may be private', this.platform, url);
        }

        if (response.status >= 400) {
          throw new ScrapingError('HTTP_ERROR', `HTTP ${response.status}: ${response.statusText}`, this.platform, url);
        }

        return response.data;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retryAttempts) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          await this.sleep(delay);
        }
      }
    }

    throw new ScrapingError(
      'FETCH_FAILED',
      `Failed to fetch after ${this.config.retryAttempts} attempts: ${lastError?.message}`,
      this.platform,
      url
    );
  }

  /**
   * Parse HTML and create Cheerio instance
   */
  protected parseHtml(html: string): cheerio.CheerioAPI {
    return cheerio.load(html);
  }

  /**
   * Create scraping context
   */
  protected createContext(url: string, html?: string): ScrapingContext {
    const context: ScrapingContext = { url };
    
    if (html) {
      context.html = html;
      context.$ = this.parseHtml(html);
    }
    
    return context;
  }

  /**
   * Extract images from HTML
   */
  protected extractImages($: cheerio.CheerioAPI, selectors: string[]): Array<{url: string, alt?: string}> {
    const images: Array<{url: string, alt?: string}> = [];
    
    selectors.forEach(selector => {
      $(selector).each((_, element) => {
        const $img = $(element);
        const src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-lazy');
        const alt = $img.attr('alt');
        
        if (src && this.isValidImageUrl(src)) {
          images.push({
            url: this.normalizeUrl(src),
            alt: alt || undefined,
          });
        }
      });
    });
    
    return this.deduplicateImages(images);
  }

  /**
   * Detect license from text content
   */
  protected detectLicense(text: string): ScrapedLicense {
    const licensePatterns = [
      { pattern: /creative\s+commons\s+attribution\s+4\.0|cc\s+by\s+4\.0/i, type: 'CC-BY' as const, confidence: 0.9 },
      { pattern: /creative\s+commons\s+attribution\s+share\s+alike|cc\s+by-sa/i, type: 'CC-BY-SA' as const, confidence: 0.9 },
      { pattern: /creative\s+commons\s+attribution\s+non\s*commercial|cc\s+by-nc/i, type: 'CC-BY-NC' as const, confidence: 0.9 },
      { pattern: /creative\s+commons\s+zero|cc0|public\s+domain/i, type: 'CC0' as const, confidence: 0.9 },
      { pattern: /gpl|gnu\s+general\s+public\s+license/i, type: 'GPL' as const, confidence: 0.8 },
      { pattern: /mit\s+license/i, type: 'MIT' as const, confidence: 0.8 },
      { pattern: /commercial|proprietary/i, type: 'Commercial' as const, confidence: 0.7 },
    ];

    for (const { pattern, type, confidence } of licensePatterns) {
      if (pattern.test(text)) {
        return {
          type,
          name: this.getLicenseName(type),
          detected: true,
          confidence,
          allowCommercialUse: this.getAllowCommercialUse(type),
          requireAttribution: this.getRequireAttribution(type),
          allowDerivatives: this.getAllowDerivatives(type),
        };
      }
    }

    // Default to custom license if no pattern matches
    return {
      type: 'Custom',
      name: 'Custom License',
      detected: false,
      confidence: 0.1,
      allowCommercialUse: false,
      requireAttribution: true,
      allowDerivatives: false,
    };
  }

  /**
   * Normalize URL (handle relative URLs, remove query params, etc.)
   */
  protected normalizeUrl(url: string, baseUrl?: string): string {
    try {
      if (url.startsWith('//')) {
        return `https:${url}`;
      }
      
      if (url.startsWith('/') && baseUrl) {
        const base = new URL(baseUrl);
        return `${base.protocol}//${base.host}${url}`;
      }
      
      if (!url.startsWith('http')) {
        return baseUrl ? new URL(url, baseUrl).href : url;
      }
      
      return url;
    } catch {
      return url;
    }
  }

  /**
   * Validate image URL
   */
  protected isValidImageUrl(url: string): boolean {
    if (!url || url.length < 10) return false;
    
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg)(\?|$)/i;
    return imageExtensions.test(url) || url.includes('image') || url.includes('thumb');
  }

  /**
   * Remove duplicate images
   */
  protected deduplicateImages(images: Array<{url: string, alt?: string}>): Array<{url: string, alt?: string}> {
    const seen = new Set<string>();
    return images.filter(img => {
      if (seen.has(img.url)) return false;
      seen.add(img.url);
      return true;
    });
  }

  /**
   * Extract text content and clean it
   */
  protected extractText($: cheerio.CheerioAPI, selector: string): string {
    return $(selector).text().trim().replace(/\s+/g, ' ');
  }

  /**
   * Extract numeric value from text
   */
  protected extractNumber(text: string): number {
    const match = text.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, ''), 10) : 0;
  }

  /**
   * Sleep utility
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get license name by type
   */
  private getLicenseName(type: ScrapedLicense['type']): string {
    const names = {
      'CC0': 'Creative Commons Zero',
      'CC-BY': 'Creative Commons Attribution 4.0',
      'CC-BY-SA': 'Creative Commons Attribution-ShareAlike',
      'CC-BY-NC': 'Creative Commons Attribution-NonCommercial',
      'CC-BY-NC-SA': 'Creative Commons Attribution-NonCommercial-ShareAlike',
      'GPL': 'GNU General Public License',
      'MIT': 'MIT License',
      'Custom': 'Custom License',
      'Commercial': 'Commercial License',
    };
    return names[type] || 'Unknown License';
  }

  /**
   * Check if license allows commercial use
   */
  private getAllowCommercialUse(type: ScrapedLicense['type']): boolean {
    return !['CC-BY-NC', 'CC-BY-NC-SA'].includes(type);
  }

  /**
   * Check if license requires attribution
   */
  private getRequireAttribution(type: ScrapedLicense['type']): boolean {
    return !['CC0', 'MIT'].includes(type);
  }

  /**
   * Check if license allows derivatives
   */
  private getAllowDerivatives(type: ScrapedLicense['type']): boolean {
    return type !== 'Commercial';
  }
}

/**
 * Custom error class for scraping operations
 */
export class ScrapingError extends Error {
  public code: string;
  public platform?: ModelSource;
  public url?: string;
  public details?: Record<string, any>;
  public retryAfter?: number;

  constructor(
    code: string,
    message: string,
    platform?: ModelSource,
    url?: string,
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'ScrapingError';
    this.code = code;
    this.platform = platform;
    this.url = url;
    this.details = details;
  }
}
