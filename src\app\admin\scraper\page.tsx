'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Download, 
  Database, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Globe,
  Package,
  Target,
  Shuffle
} from 'lucide-react';

export default function ScraperAdminPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [modelCount, setModelCount] = useState(50);
  const [lastResult, setLastResult] = useState<any>(null);
  const { toast } = useToast();

  const handleScrapeAction = async (action: string, count?: number) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          count: count || modelCount
        }),
      });

      const result = await response.json();
      setLastResult(result);

      if (result.success) {
        toast({
          title: 'Успіх!',
          description: result.message,
          variant: 'default'
        });
      } else {
        toast({
          title: 'Помилка',
          description: result.message,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Помилка скрапінгу:', error);
      toast({
        title: 'Помилка',
        description: 'Не вдалося виконати скрапінг',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkModelsCount = async () => {
    try {
      const response = await fetch('/api/scraped-models?limit=1');
      const result = await response.json();
      return result.total || 0;
    } catch (error) {
      return 0;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Адміністрування скрапера</h1>
          <p className="text-muted-foreground">
            Управління скрапінгом 3D моделей з популярних платформ
          </p>
        </div>

        {/* Статистика */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Моделей в базі</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {lastResult?.total || '0'}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Останній скрапінг</CardTitle>
              {lastResult?.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
            </CardHeader>
            <CardContent>
              <div className="text-sm text-muted-foreground">
                {lastResult ? (
                  lastResult.success ? 'Успішно' : 'Помилка'
                ) : 'Не виконувався'}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Статус</CardTitle>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
            </CardHeader>
            <CardContent>
              <div className="text-sm text-muted-foreground">
                {isLoading ? 'Виконується...' : 'Готовий'}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Налаштування */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Налаштування скрапінгу</CardTitle>
            <CardDescription>
              Встановіть кількість моделей для скрапінгу
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <Label htmlFor="modelCount">Кількість моделей:</Label>
              <Input
                id="modelCount"
                type="number"
                value={modelCount}
                onChange={(e) => setModelCount(parseInt(e.target.value) || 50)}
                min="1"
                max="200"
                className="w-32"
              />
            </div>
          </CardContent>
        </Card>

        {/* Дії скрапінгу */}
        <Tabs defaultValue="quick" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="quick">Швидкий старт</TabsTrigger>
            <TabsTrigger value="platforms">Платформи</TabsTrigger>
            <TabsTrigger value="advanced">Розширені</TabsTrigger>
          </TabsList>

          <TabsContent value="quick" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shuffle className="h-5 w-5" />
                  Швидкий старт
                </CardTitle>
                <CardDescription>
                  Найшвидший спосіб наповнити базу даних моделями
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={() => handleScrapeAction('fake-data')}
                  disabled={isLoading}
                  className="w-full"
                  size="lg"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Database className="mr-2 h-4 w-4" />
                  )}
                  Згенерувати тестові дані ({modelCount} моделей)
                </Button>
                <p className="text-sm text-muted-foreground">
                  Швидко створює тестові моделі для демонстрації функціоналу
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="platforms" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Printables.com
                  </CardTitle>
                  <CardDescription>
                    Скрапінг з найбільшої безкоштовної платформи
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => handleScrapeAction('scrape-printables')}
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Download className="mr-2 h-4 w-4" />
                    )}
                    Скрапити Printables
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Thangs.com
                  </CardTitle>
                  <CardDescription>
                    Скрапінг з професійної платформи
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => handleScrapeAction('scrape-thangs')}
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Download className="mr-2 h-4 w-4" />
                    )}
                    Скрапити Thangs
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Повний скрапінг
                  </CardTitle>
                  <CardDescription>
                    Скрапінг з усіх доступних платформ одночасно
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => handleScrapeAction('populate')}
                    disabled={isLoading}
                    className="w-full"
                    variant="default"
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Globe className="mr-2 h-4 w-4" />
                    )}
                    Скрапити всі платформи
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Скрапінг за категоріями</CardTitle>
                  <CardDescription>
                    Детальний скрапінг по кожній категорії окремо
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => handleScrapeAction('populate-categories')}
                    disabled={isLoading}
                    className="w-full"
                    variant="outline"
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Package className="mr-2 h-4 w-4" />
                    )}
                    Скрапити за категоріями
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Результат останнього скрапінгу */}
        {lastResult && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Результат останнього скрапінгу</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md text-sm overflow-auto">
                {JSON.stringify(lastResult, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
