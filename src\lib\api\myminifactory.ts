/**
 * MyMiniFactory scraper implementation
 * Supports scraping models from MyMiniFactory.com
 */

import { BaseScraper } from '@/lib/scraping/base-scraper';
import { ScrapedModel, ScrapedImage, ScrapedFile, ScrapedDesigner } from '@/types/models';
import { ScrapingError } from '@/lib/scraping/base-scraper';
import * as cheerio from 'cheerio';

export class MyMiniFactoryScraper extends BaseScraper {
  constructor() {
    super('myminifactory', {
      userAgent: 'Mozilla/5.0 (compatible; 3DMarketplace-MyMiniFactory/1.0)',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimit: 10, // Conservative rate limit for MyMiniFactory
    });
  }

  /**
   * Validate if URL is from MyMiniFactory
   */
  validateUrl(url: string): boolean {
    return /^https?:\/\/(www\.)?myminifactory\.com\/object\/3d-print\/[\w-]+\d+/.test(url);
  }

  /**
   * Extract model ID from MyMiniFactory URL
   */
  extractModelId(url: string): string | null {
    const match = url.match(/myminifactory\.com\/object\/3d-print\/[\w-]+-(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Main scraping method for MyMiniFactory models
   */
  async scrapeModel(url: string): Promise<ScrapedModel> {
    if (!this.validateUrl(url)) {
      throw new ScrapingError(
        'INVALID_URL',
        'URL is not a valid MyMiniFactory model URL',
        this.platform,
        url
      );
    }

    const modelId = this.extractModelId(url);
    if (!modelId) {
      throw new ScrapingError(
        'INVALID_MODEL_ID',
        'Could not extract model ID from URL',
        this.platform,
        url
      );
    }

    try {
      // Fetch the HTML content
      const html = await this.fetchHtml(url);
      const $ = this.parseHtml(html);

      // Extract basic information
      const title = this.extractTitle($);
      const description = this.extractDescription($);
      const images = this.extractImages($);
      const files = this.extractFiles($, url);
      const designer = this.extractDesigner($);
      const stats = this.extractStats($);
      const tags = this.extractTags($);
      const category = this.extractCategory($);
      const license = this.detectLicense($.html());
      const pricing = this.extractPricing($);

      return {
        title,
        description,
        summary: this.extractSummary($),
        images,
        thumbnail: images.length > 0 ? images[0].url : '',
        files,
        fileFormats: this.extractFileFormats(files),
        totalSize: this.calculateTotalSize(files),
        designer,
        tags,
        category,
        license,
        stats,
        platform: 'myminifactory',
        originalId: modelId,
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        isFree: pricing.isFree,
        price: pricing.price,
        currency: pricing.currency,
      };
    } catch (error) {
      if (error instanceof ScrapingError) {
        throw error;
      }
      throw new ScrapingError(
        'SCRAPING_FAILED',
        `Failed to scrape MyMiniFactory model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.platform,
        url
      );
    }
  }

  /**
   * Extract model title
   */
  private extractTitle($: cheerio.CheerioAPI): string {
    const selectors = [
      'h1.object-title',
      '.object-header h1',
      '.product-title h1',
      'h1'
    ];

    for (const selector of selectors) {
      const title = $(selector).first().text().trim();
      if (title) {
        return this.sanitizeText(title);
      }
    }

    return 'Untitled Model';
  }

  /**
   * Extract model description
   */
  private extractDescription($: cheerio.CheerioAPI): string {
    const selectors = [
      '.object-description',
      '.product-description',
      '.description-content',
      '#description'
    ];

    for (const selector of selectors) {
      const description = $(selector).first().html();
      if (description) {
        return this.sanitizeHtml(description);
      }
    }

    return '';
  }

  /**
   * Extract summary
   */
  private extractSummary($: cheerio.CheerioAPI): string {
    const summary = $('.object-summary, .product-summary').first().text().trim();
    return summary ? this.sanitizeText(summary) : '';
  }

  /**
   * Extract images
   */
  protected extractImages($: cheerio.CheerioAPI): ScrapedImage[] {
    const images: ScrapedImage[] = [];
    const imageElements = $('.object-gallery img, .product-images img, .carousel-item img');

    imageElements.each((_, element) => {
      const $img = $(element);
      let src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-original');
      
      if (src) {
        // Convert relative URLs to absolute
        if (src.startsWith('//')) {
          src = 'https:' + src;
        } else if (src.startsWith('/')) {
          src = 'https://myminifactory.com' + src;
        }

        // Get high-resolution version if available
        if (src.includes('/thumb/')) {
          src = src.replace('/thumb/', '/large/');
        }

        const alt = $img.attr('alt') || '';
        
        images.push({
          id: `img_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
          url: src,
          alt: this.sanitizeText(alt),
          width: parseInt($img.attr('width') || '0') || undefined,
          height: parseInt($img.attr('height') || '0') || undefined,
        });
      }
    });

    return this.deduplicateImages(images);
  }

  /**
   * Extract files information
   */
  private extractFiles($: cheerio.CheerioAPI, baseUrl: string): ScrapedFile[] {
    const files: ScrapedFile[] = [];
    const fileElements = $('.file-item, .download-item, .object-file');

    fileElements.each((_, element) => {
      const $file = $(element);
      const name = $file.find('.file-name, .filename').text().trim() || 
                   $file.text().trim();
      
      let downloadUrl = $file.find('a').attr('href') || $file.attr('href');
      
      if (downloadUrl && name) {
        // Convert relative URLs to absolute
        if (downloadUrl.startsWith('/')) {
          downloadUrl = 'https://myminifactory.com' + downloadUrl;
        }

        const sizeText = $file.find('.file-size, .size').text().trim();
        const size = this.parseFileSize(sizeText);
        const format = this.extractFileFormat(name);

        files.push({
          id: `file_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
          name: this.sanitizeText(name),
          url: downloadUrl,
          downloadUrl,
          size,
          format,
        });
      }
    });

    return files;
  }

  /**
   * Extract designer information
   */
  private extractDesigner($: cheerio.CheerioAPI): ScrapedDesigner {
    const designerElement = $('.designer-info, .creator-info, .object-creator');
    const name = designerElement.find('a').text().trim() || 
                 designerElement.text().trim() || 
                 'Unknown Designer';
    
    const profileUrl = designerElement.find('a').attr('href');
    const avatarUrl = $('.designer-avatar img, .creator-avatar img').attr('src');

    return {
      id: `designer_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      name: this.sanitizeText(name),
      profileUrl: profileUrl ? (profileUrl.startsWith('/') ? 'https://myminifactory.com' + profileUrl : profileUrl) : undefined,
      avatar: avatarUrl ? (avatarUrl.startsWith('/') ? 'https://myminifactory.com' + avatarUrl : avatarUrl) : undefined,
    };
  }

  /**
   * Extract statistics
   */
  private extractStats($: cheerio.CheerioAPI) {
    const stats = {
      views: 0,
      downloads: 0,
      likes: 0,
      comments: 0,
    };

    // Extract views
    const viewsText = $('.object-stats .views, .stat-views').text();
    stats.views = this.parseNumber(viewsText);

    // Extract downloads
    const downloadsText = $('.object-stats .downloads, .stat-downloads').text();
    stats.downloads = this.parseNumber(downloadsText);

    // Extract likes
    const likesText = $('.object-stats .likes, .stat-likes, .like-count').text();
    stats.likes = this.parseNumber(likesText);

    // Extract comments
    const commentsText = $('.object-stats .comments, .stat-comments, .comment-count').text();
    stats.comments = this.parseNumber(commentsText);

    return stats;
  }

  /**
   * Extract tags
   */
  private extractTags($: cheerio.CheerioAPI): string[] {
    const tags: string[] = [];
    const tagElements = $('.object-tags a, .tags a, .tag');

    tagElements.each((_, element) => {
      const tag = $(element).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(this.sanitizeText(tag));
      }
    });

    return tags;
  }

  /**
   * Extract category
   */
  private extractCategory($: cheerio.CheerioAPI): string {
    const categoryElement = $('.object-category, .category, .breadcrumb a');
    const category = categoryElement.last().text().trim();
    return category ? this.sanitizeText(category) : 'Other';
  }

  /**
   * Extract pricing information
   */
  private extractPricing($: cheerio.CheerioAPI) {
    const priceElement = $('.price, .object-price, .product-price');
    const priceText = priceElement.text().trim();
    
    if (priceText.toLowerCase().includes('free') || priceText === '') {
      return { isFree: true, price: 0, currency: 'USD' };
    }

    const priceMatch = priceText.match(/[\d.,]+/);
    const price = priceMatch ? parseFloat(priceMatch[0].replace(',', '.')) : 0;
    
    // Extract currency
    const currencyMatch = priceText.match(/[£$€]/);
    let currency = 'USD';
    if (currencyMatch) {
      switch (currencyMatch[0]) {
        case '£': currency = 'GBP'; break;
        case '€': currency = 'EUR'; break;
        case '$': currency = 'USD'; break;
      }
    }

    return { isFree: price === 0, price, currency };
  }
}

// Export singleton instance
export const myMiniFactoryScraper = new MyMiniFactoryScraper();

// Export import function for easy use
export async function importFromMyMiniFactory(url: string): Promise<{ success: boolean; model?: ScrapedModel; error?: string }> {
  try {
    const model = await myMiniFactoryScraper.scrapeModel(url);
    return { success: true, model };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
