/**
 * Thingiverse scraper implementation
 * Supports scraping models from Thingiverse.com
 */

import { BaseScraper } from '@/lib/scraping/base-scraper';
import { ScrapedModel, ScrapedImage, ScrapedFile, ScrapedDesigner } from '@/types/models';
import { ScrapingError } from '@/lib/scraping/base-scraper';
import * as cheerio from 'cheerio';

export class ThingiverseScraper extends BaseScraper {
  constructor() {
    super('thingiverse', {
      userAgent: 'Mozilla/5.0 (compatible; 3DMarketplace-Thingiverse/1.0)',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimit: 8, // Conservative rate limit for Thingiverse
    });
  }

  /**
   * Validate if URL is from Thingiverse
   */
  validateUrl(url: string): boolean {
    return /^https?:\/\/(www\.)?thingiverse\.com\/thing:\d+/.test(url);
  }

  /**
   * Extract model ID from Thingiverse URL
   */
  extractModelId(url: string): string | null {
    const match = url.match(/thingiverse\.com\/thing:(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Main scraping method for Thingiverse models
   */
  async scrapeModel(url: string): Promise<ScrapedModel> {
    if (!this.validateUrl(url)) {
      throw new ScrapingError(
        'INVALID_URL',
        'URL is not a valid Thingiverse model URL',
        this.platform,
        url
      );
    }

    const modelId = this.extractModelId(url);
    if (!modelId) {
      throw new ScrapingError(
        'INVALID_MODEL_ID',
        'Could not extract model ID from URL',
        this.platform,
        url
      );
    }

    try {
      // Fetch the HTML content
      const html = await this.fetchHtml(url);
      const $ = this.parseHtml(html);

      // Extract basic information
      const title = this.extractTitle($);
      const description = this.extractDescription($);
      const images = this.extractImages($);
      const files = this.extractFiles($, url);
      const designer = this.extractDesigner($);
      const stats = this.extractStats($);
      const tags = this.extractTags($);
      const category = this.extractCategory($);
      const license = this.detectLicense($.html());

      return {
        title,
        description,
        summary: this.extractSummary($),
        images,
        thumbnail: images.length > 0 ? images[0].url : '',
        files,
        fileFormats: this.extractFileFormats(files),
        totalSize: this.calculateTotalSize(files),
        designer,
        tags,
        category,
        license,
        stats,
        platform: 'thingiverse',
        originalId: modelId,
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        isFree: true, // Thingiverse models are typically free
      };
    } catch (error) {
      if (error instanceof ScrapingError) {
        throw error;
      }
      throw new ScrapingError(
        'SCRAPING_FAILED',
        `Failed to scrape Thingiverse model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.platform,
        url
      );
    }
  }

  /**
   * Extract model title
   */
  private extractTitle($: cheerio.CheerioAPI): string {
    const selectors = [
      'h1[data-track-action="thing_page_title_click"]',
      'h1.thing-header-data h1',
      '.thing-header h1',
      'h1'
    ];

    for (const selector of selectors) {
      const title = $(selector).first().text().trim();
      if (title) {
        return this.sanitizeText(title);
      }
    }

    return 'Untitled Model';
  }

  /**
   * Extract model description
   */
  private extractDescription($: cheerio.CheerioAPI): string {
    const selectors = [
      '.thing-info-content .description',
      '.thing-summary',
      '.thing-description',
      '#description'
    ];

    for (const selector of selectors) {
      const description = $(selector).first().html();
      if (description) {
        return this.sanitizeHtml(description);
      }
    }

    return '';
  }

  /**
   * Extract summary
   */
  private extractSummary($: cheerio.CheerioAPI): string {
    const summary = $('.thing-summary').first().text().trim();
    return summary ? this.sanitizeText(summary) : '';
  }

  /**
   * Extract images
   */
  private extractImages($: cheerio.CheerioAPI): ScrapedImage[] {
    const images: ScrapedImage[] = [];
    const imageElements = $('.thing-gallery img, .thing-image img, .carousel-inner img');

    imageElements.each((_, element) => {
      const $img = $(element);
      let src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-original');
      
      if (src) {
        // Convert relative URLs to absolute
        if (src.startsWith('//')) {
          src = 'https:' + src;
        } else if (src.startsWith('/')) {
          src = 'https://thingiverse.com' + src;
        }

        // Get high-resolution version if available
        if (src.includes('/thumb/')) {
          src = src.replace('/thumb/', '/display_large/');
        }

        const alt = $img.attr('alt') || '';
        
        images.push({
          url: src,
          alt: this.sanitizeText(alt),
          width: parseInt($img.attr('width') || '0') || undefined,
          height: parseInt($img.attr('height') || '0') || undefined,
        });
      }
    });

    return this.deduplicateImages(images);
  }

  /**
   * Extract files information
   */
  private extractFiles($: cheerio.CheerioAPI, baseUrl: string): ScrapedFile[] {
    const files: ScrapedFile[] = [];
    const fileElements = $('.thing-file, .file-info, .download-link');

    fileElements.each((_, element) => {
      const $file = $(element);
      const name = $file.find('.file-name, .filename').text().trim() || 
                   $file.text().trim();
      
      let downloadUrl = $file.find('a').attr('href') || $file.attr('href');
      
      if (downloadUrl && name) {
        // Convert relative URLs to absolute
        if (downloadUrl.startsWith('/')) {
          downloadUrl = 'https://thingiverse.com' + downloadUrl;
        }

        const sizeText = $file.find('.file-size, .size').text().trim();
        const size = this.parseFileSize(sizeText);
        const format = this.extractFileFormat(name);

        files.push({
          name: this.sanitizeText(name),
          url: downloadUrl,
          size,
          format,
          type: this.getFileType(format),
        });
      }
    });

    return files;
  }

  /**
   * Extract designer information
   */
  private extractDesigner($: cheerio.CheerioAPI): ScrapedDesigner {
    const designerElement = $('.thing-header-data .user-name, .designer-info, .user-info');
    const name = designerElement.find('a').text().trim() || 
                 designerElement.text().trim() || 
                 'Unknown Designer';
    
    const profileUrl = designerElement.find('a').attr('href');
    const avatarUrl = $('.user-avatar img, .designer-avatar img').attr('src');

    return {
      name: this.sanitizeText(name),
      profileUrl: profileUrl ? (profileUrl.startsWith('/') ? 'https://thingiverse.com' + profileUrl : profileUrl) : undefined,
      avatarUrl: avatarUrl ? (avatarUrl.startsWith('/') ? 'https://thingiverse.com' + avatarUrl : avatarUrl) : undefined,
    };
  }

  /**
   * Extract statistics
   */
  private extractStats($: cheerio.CheerioAPI) {
    const stats = {
      views: 0,
      downloads: 0,
      likes: 0,
      comments: 0,
    };

    // Extract views
    const viewsText = $('.thing-stats .views, .stat-views').text();
    stats.views = this.parseNumber(viewsText);

    // Extract downloads
    const downloadsText = $('.thing-stats .downloads, .stat-downloads').text();
    stats.downloads = this.parseNumber(downloadsText);

    // Extract likes
    const likesText = $('.thing-stats .likes, .stat-likes, .like-count').text();
    stats.likes = this.parseNumber(likesText);

    // Extract comments
    const commentsText = $('.thing-stats .comments, .stat-comments, .comment-count').text();
    stats.comments = this.parseNumber(commentsText);

    return stats;
  }

  /**
   * Extract tags
   */
  private extractTags($: cheerio.CheerioAPI): string[] {
    const tags: string[] = [];
    const tagElements = $('.thing-tags a, .tags a, .tag');

    tagElements.each((_, element) => {
      const tag = $(element).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(this.sanitizeText(tag));
      }
    });

    return tags;
  }

  /**
   * Extract category
   */
  private extractCategory($: cheerio.CheerioAPI): string {
    const categoryElement = $('.thing-category, .category, .breadcrumb a');
    const category = categoryElement.last().text().trim();
    return category ? this.sanitizeText(category) : 'Other';
  }
}

// Export singleton instance
export const thingiverseScraper = new ThingiverseScraper();

// Export import function for easy use
export async function importFromThingiverse(url: string): Promise<{ success: boolean; model?: ScrapedModel; error?: string }> {
  try {
    const model = await thingiverseScraper.scrapeModel(url);
    return { success: true, model };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
