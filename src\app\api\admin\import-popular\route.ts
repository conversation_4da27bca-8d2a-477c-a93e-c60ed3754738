import { NextRequest, NextResponse } from 'next/server';
import { popularModelsImporter } from '@/scripts/import-popular-models';
import { errorHandler } from '@/lib/error-handling/enhanced-error-handler';
import { cloudflareMonitoring } from '@/lib/observability/cloudflare-monitoring';

/**
 * POST /api/admin/import-popular
 * Запускає імпорт найпопулярніших моделей
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      type = 'all', // 'all', 'sample', 'platform'
      platform, 
      modelsPerPlatform = 3,
      adminKey 
    } = body;

    // Простий захист адмін ендпоінту
    if (adminKey !== process.env.ADMIN_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Unauthorized access',
            code: 'UNAUTHORIZED'
          }
        },
        { status: 401 }
      );
    }

    await cloudflareMonitoring.logEvent({
      level: 'info',
      message: `Popular models import started: ${type}`,
      service: 'admin-api',
      metadata: { type, platform, modelsPerPlatform }
    });

    // Запуск імпорту в фоновому режимі
    const importPromise = (async () => {
      try {
        switch (type) {
          case 'all':
            await popularModelsImporter.importAllPopularModels();
            break;
          case 'sample':
            await popularModelsImporter.importSampleModels(modelsPerPlatform);
            break;
          case 'platform':
            if (!platform) {
              throw new Error('Platform is required for platform import');
            }
            await popularModelsImporter.importFromPlatform(platform);
            break;
          default:
            throw new Error(`Unknown import type: ${type}`);
        }
      } catch (error) {
        await errorHandler.handleError(error as Error, {
          url: request.url,
          userAgent: request.headers.get('user-agent') || undefined
        });
      }
    })();

    // Не чекаємо завершення, повертаємо відповідь одразу
    return NextResponse.json({
      success: true,
      data: {
        message: 'Popular models import started successfully',
        type,
        platform,
        modelsPerPlatform,
        estimatedDuration: type === 'all' ? '15-30 minutes' : 
                          type === 'sample' ? '5-10 minutes' : '3-8 minutes',
        monitoringUrl: '/api/monitoring/metrics'
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/import-popular/progress
 * Отримує прогрес поточного імпорту
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');

    if (adminKey !== process.env.ADMIN_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Unauthorized access',
            code: 'UNAUTHORIZED'
          }
        },
        { status: 401 }
      );
    }

    const progress = popularModelsImporter.getProgress();
    
    return NextResponse.json({
      success: true,
      data: {
        progress,
        isActive: progress.some(p => p.completed < p.total),
        summary: {
          totalPlatforms: progress.length,
          totalModels: progress.reduce((sum, p) => sum + p.total, 0),
          completedModels: progress.reduce((sum, p) => sum + p.completed, 0),
          successfulModels: progress.reduce((sum, p) => sum + p.successful, 0),
          failedModels: progress.reduce((sum, p) => sum + p.failed, 0)
        }
      }
    });

  } catch (error) {
    const enhancedError = await errorHandler.handleError(error as Error, {
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined
    });

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: enhancedError.userMessage,
          code: enhancedError.code,
          category: enhancedError.category
        }
      },
      { status: 500 }
    );
  }
}
