# 🚀 Деплой 3D Marketplace

Інструкції для деплою вашого 3D маркетплейсу на різних платформах.

## 🌟 Рекомендований деплой: Vercel

### 1. Підготовка проекту

```bash
# Переконайтесь, що проект збирається без помилок
npm run build

# Перевірте, що всі залежності встановлені
npm install
```

### 2. Деплой на Vercel

#### Варіант A: Через GitHub (рекомендовано)

1. **Завантажте код на GitHub:**
```bash
git init
git add .
git commit -m "Initial commit: 3D Marketplace"
git branch -M main
git remote add origin https://github.com/your-username/3d-marketplace.git
git push -u origin main
```

2. **Підключіть до Vercel:**
   - Перейдіть на https://vercel.com
   - Натисніть "New Project"
   - Імпортуйте ваш GitHub репозиторій
   - Vercel автоматично визначить Next.js

3. **Налаштуйте змінні середовища:**
   - В панелі Vercel перейдіть в Settings → Environment Variables
   - Додайте необхідні змінні з `.env.example`

#### Варіант B: Через Vercel CLI

```bash
# Встановіть Vercel CLI
npm i -g vercel

# Увійдіть в акаунт
vercel login

# Деплой проекту
vercel

# Для продакшн деплою
vercel --prod
```

### 3. Налаштування змінних середовища

Обов'язкові змінні для Vercel:

```env
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-secret-key-here
NODE_ENV=production
```

### 4. Налаштування домену (опціонально)

1. В панелі Vercel перейдіть в Settings → Domains
2. Додайте ваш кастомний домен
3. Налаштуйте DNS записи згідно з інструкціями

## 🐳 Альтернативний деплой: Docker

### 1. Створіть Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 2. Збудуйте та запустіть

```bash
# Збудуйте образ
docker build -t 3d-marketplace .

# Запустіть контейнер
docker run -p 3000:3000 3d-marketplace
```

## ☁️ Деплой на інших платформах

### Netlify

1. Підключіть GitHub репозиторій
2. Build command: `npm run build`
3. Publish directory: `.next`
4. Додайте змінні середовища

### Railway

```bash
# Встановіть Railway CLI
npm install -g @railway/cli

# Увійдіть в акаунт
railway login

# Ініціалізуйте проект
railway init

# Деплой
railway up
```

### DigitalOcean App Platform

1. Створіть новий App
2. Підключіть GitHub репозиторій
3. Налаштуйте build та run команди
4. Додайте змінні середовища

## 🔧 Налаштування після деплою

### 1. Перевірте функціональність

- ✅ Головна сторінка завантажується
- ✅ Маркетплейс працює
- ✅ API endpoints відповідають
- ✅ Адмін панель доступна

### 2. Налаштуйте скрапінг

**Важливо:** Puppeteer може не працювати на деяких платформах через обмеження.

Для Vercel рекомендується:
- Використовувати тільки генерацію тестових даних
- Або налаштувати зовнішній сервіс для скрапінгу

### 3. Налаштуйте моніторинг

```bash
# Додайте Vercel Analytics
npm install @vercel/analytics

# Додайте в _app.tsx
import { Analytics } from '@vercel/analytics/react'

export default function App({ Component, pageProps }) {
  return (
    <>
      <Component {...pageProps} />
      <Analytics />
    </>
  )
}
```

## 🚨 Важливі зауваження

### Обмеження Vercel

- **Функції:** Максимум 10 секунд виконання (Hobby план)
- **Puppeteer:** Може не працювати через розмір та залежності
- **Файли:** Обмеження на розмір завантажених файлів

### Рекомендації для продакшну

1. **База даних:** Використовуйте PostgreSQL або MongoDB
2. **Файлове сховище:** AWS S3, Cloudinary або подібні
3. **Скрапінг:** Винесіть в окремий сервіс або використовуйте cron jobs
4. **Кешування:** Redis для кешування API відповідей

## 🔗 Корисні посилання

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Docker with Next.js](https://nextjs.org/docs/deployment#docker-image)

## 🆘 Усунення проблем

### Помилка збірки

```bash
# Очистіть кеш
npm run clean
rm -rf .next node_modules
npm install
npm run build
```

### Проблеми з Puppeteer

```bash
# Для локального тестування
npm install puppeteer-core chrome-aws-lambda

# Або використовуйте Playwright
npm install playwright-aws-lambda
```

### Проблеми з змінними середовища

- Перевірте, що всі змінні додані в панелі управління
- Переконайтесь, що `NEXTAUTH_URL` вказує на правильний домен
- Перезапустіть деплой після зміни змінних

## ✅ Готово!

Ваш 3D маркетплейс готовий до використання! 🎉

Не забудьте:
- Налаштувати домен
- Додати SSL сертифікат (автоматично на Vercel)
- Налаштувати аналітику
- Протестувати всі функції
