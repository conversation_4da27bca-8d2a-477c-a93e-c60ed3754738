# Розгортання 3D-Маркетплейсу на Cloudflare

## Зміст

1. [Вступ](#вступ)
2. [Вимоги](#вимоги)
3. [Налаштування Cloudflare Pages](#налаштування-cloudflare-pages)
   - [Створення проекту](#створення-проекту)
   - [Налаштування збірки](#налаштування-збірки)
   - [Змінні середовища](#змінні-середовища)
4. [Налаштування CI/CD](#налаштування-cicd)
   - [GitHub Actions](#github-actions)
   - [Автоматичне розгортання](#автоматичне-розгортання)
5. [Налаштування домену](#налаштування-домену)
   - [Користувацький домен](#користувацький-домен)
   - [SSL/TLS](#ssltls)
6. [Налаштування кешування](#налаштування-кешування)
   - [Кешування статичних файлів](#кешування-статичних-файлів)
   - [Кешування API-відповідей](#кешування-api-відповідей)
7. [Моніторинг та аналітика](#моніторинг-та-аналітика)
   - [Cloudflare Analytics](#cloudflare-analytics)
   - [Логування](#логування)
8. [Оптимізація продуктивності](#оптимізація-продуктивності)
   - [Cloudflare Workers](#cloudflare-workers)
   - [Оптимізація зображень](#оптимізація-зображень)
9. [Рекомендації та найкращі практики](#рекомендації-та-найкращі-практики)
10. [Усунення несправностей](#усунення-несправностей)

## Вступ

Цей документ описує процес розгортання 3D-маркетплейсу на Cloudflare Pages. Cloudflare Pages - це платформа для розгортання статичних сайтів та JAMstack-додатків, яка забезпечує швидку доставку контенту через глобальну мережу Cloudflare.

**Оновлено: 14 травня 2024** - Додано нові файли конфігурації та інструкції для автоматичного розгортання.

## Вимоги

Для розгортання проекту на Cloudflare Pages вам знадобиться:

1. Акаунт Cloudflare
2. Репозиторій GitHub з проектом
3. Налаштований проект Next.js
4. Доступ до DNS-налаштувань домену (якщо використовується користувацький домен)

## Файли конфігурації

Проект вже містить необхідні файли конфігурації для розгортання на Cloudflare Pages:

1. **`_routes.json`** - Конфігурація маршрутів для Cloudflare Pages
2. **`wrangler.toml`** - Конфігурація Cloudflare Workers
3. **`.github/workflows/cloudflare-pages.yml`** - Конфігурація GitHub Actions для автоматичного розгортання
4. **`next.config.js`** - Налаштування Next.js для сумісності з Cloudflare Pages

## Налаштування Cloudflare Pages

### Створення проекту

1. Увійдіть в акаунт Cloudflare
2. Перейдіть до розділу "Pages"
3. Натисніть "Create a project"
4. Виберіть "Connect to Git"
5. Авторизуйтеся в GitHub та виберіть репозиторій з проектом
6. Натисніть "Begin setup"

### Налаштування збірки

Налаштуйте параметри збірки:

- **Project name**: `3d-marketplace` (або інша назва)
- **Production branch**: `main` (або інша основна гілка)
- **Framework preset**: `Next.js`
- **Build command**: `npm run build`
- **Build output directory**: `.next`
- **Root directory**: `/` (або інша, якщо проект знаходиться в підпапці)

Додаткові налаштування для Next.js:

- **Node.js version**: `18` (або вище)
- **Include development dependencies**: `Yes`

### Змінні середовища

Додайте необхідні змінні середовища:

1. Перейдіть до вкладки "Settings" > "Environment variables"
2. Додайте змінні середовища з файлу `.env.example`
3. Для кожної змінної вкажіть, чи повинна вона бути доступна тільки для продакшн-середовища або для всіх середовищ

Приклад змінних середовища:

- `NEXT_PUBLIC_API_URL`: URL API-сервера
- `NEXT_PUBLIC_SPLINE_API_KEY`: API-ключ для Spline
- `NEXT_PUBLIC_SITE_URL`: URL сайту

## Налаштування CI/CD

### GitHub Actions

Для автоматизації процесу розгортання можна використовувати GitHub Actions. Створіть файл `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Cloudflare Pages

on:
  push:
    branches:
      - main  # або інша основна гілка

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build project
        run: npm run build

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: 3d-marketplace
          directory: .next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
```

Для використання цього workflow необхідно додати секрети в налаштуваннях репозиторію GitHub:

1. `CLOUDFLARE_API_TOKEN`: API-токен Cloudflare з дозволом на розгортання Pages
2. `CLOUDFLARE_ACCOUNT_ID`: ID акаунта Cloudflare

### Автоматичне розгортання

Cloudflare Pages автоматично розгортає проект при пуші в основну гілку. Для кожного пулл-реквесту створюється превью-середовище.

Налаштування автоматичного розгортання:

1. Перейдіть до вкладки "Settings" > "Builds & deployments"
2. Налаштуйте "Production deployments" та "Preview deployments"
3. Виберіть гілки для автоматичного розгортання

## Налаштування домену

### Користувацький домен

Для налаштування користувацького домену:

1. Перейдіть до вкладки "Custom domains"
2. Натисніть "Set up a custom domain"
3. Введіть домен (наприклад, `3d-marketplace.com`)
4. Виберіть метод налаштування:
   - **Cloudflare as your DNS provider**: якщо домен вже використовує Cloudflare для DNS
   - **Another DNS provider**: якщо домен використовує інший DNS-провайдер

Якщо використовується інший DNS-провайдер, додайте CNAME-запис:

- **Name**: `www` або `@` (для кореневого домену)
- **Target**: `<project-name>.pages.dev`
- **TTL**: Auto або 3600

### SSL/TLS

Cloudflare Pages автоматично налаштовує SSL/TLS для всіх доменів. За замовчуванням використовується режим "Full".

Для налаштування SSL/TLS:

1. Перейдіть до розділу "SSL/TLS"
2. Виберіть режим шифрування:
   - **Off**: без шифрування (не рекомендується)
   - **Flexible**: шифрування між клієнтом та Cloudflare
   - **Full**: шифрування між клієнтом, Cloudflare та сервером
   - **Full (strict)**: шифрування з перевіркою сертифіката сервера

Рекомендується використовувати режим "Full" або "Full (strict)".

## Налаштування кешування

### Кешування статичних файлів

Cloudflare автоматично кешує статичні файли. Для налаштування кешування:

1. Перейдіть до розділу "Caching"
2. Налаштуйте "Browser Cache TTL" (рекомендується 4 години або більше)
3. Налаштуйте "Edge Cache TTL" (рекомендується 1 день або більше)

Для Next.js можна налаштувати кешування в `next.config.js`:

```js
module.exports = {
  async headers() {
    return [
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/(.*)\\.(?:jpg|jpeg|gif|png|svg|webp)$',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/(.*)\\.(?:js|css)$',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};
```

### Кешування API-відповідей

Для кешування API-відповідей можна використовувати Cloudflare Workers або налаштувати кешування в Next.js API Routes:

```js
// pages/api/example.js
export default async function handler(req, res) {
  res.setHeader('Cache-Control', 'public, s-maxage=60, stale-while-revalidate=600');

  // Логіка API
  const data = { example: 'data' };

  res.status(200).json(data);
}
```

## Моніторинг та аналітика

### Cloudflare Analytics

Cloudflare надає вбудовану аналітику для всіх проектів Pages:

1. Перейдіть до вкладки "Analytics"
2. Перегляньте статистику:
   - Кількість запитів
   - Обсяг трафіку
   - Статуси відповідей
   - Популярні URL
   - Географічний розподіл

### Логування

Для налаштування логування:

1. Перейдіть до вкладки "Logs"
2. Налаштуйте "Access logs" та "Error logs"
3. За потреби налаштуйте інтеграцію з зовнішніми сервісами логування

## Оптимізація продуктивності

### Cloudflare Workers

Для додаткової оптимізації можна використовувати Cloudflare Workers:

1. Створіть файл `workers-site/index.js`:

```js
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  const url = new URL(request.url);

  // Кешування для 3D-моделей
  if (url.pathname.match(/\.(gltf|glb|obj|stl)$/)) {
    const response = await fetch(request);
    const newResponse = new Response(response.body, response);
    newResponse.headers.set('Cache-Control', 'public, max-age=31536000');
    return newResponse;
  }

  // Звичайний запит
  return fetch(request);
}
```

1. Налаштуйте Workers у файлі `wrangler.toml`

### Оптимізація зображень

Cloudflare автоматично оптимізує зображення через Cloudflare Images. Для додаткової оптимізації:

1. Використовуйте компонент `Image` з Next.js
2. Налаштуйте автоматичну оптимізацію в `next.config.js`:

```js
module.exports = {
  images: {
    domains: ['your-domain.com'],
    formats: ['image/avif', 'image/webp'],
  },
};
```

## Рекомендації та найкращі практики

1. **Використовуйте статичну генерацію** - Використовуйте `getStaticProps` та `getStaticPaths` для генерації статичних сторінок
2. **Оптимізуйте 3D-моделі** - Зменшуйте розмір 3D-моделей для швидшого завантаження
3. **Використовуйте ліниве завантаження** - Завантажуйте 3D-моделі тільки коли вони потрібні
4. **Налаштуйте кешування** - Правильно налаштуйте кешування для статичних файлів та API-відповідей
5. **Моніторте продуктивність** - Регулярно перевіряйте продуктивність сайту

## Усунення несправностей

### Проблеми зі збіркою

Якщо виникають проблеми зі збіркою:

1. Перевірте логи збірки в Cloudflare Pages
2. Переконайтеся, що всі залежності вказані в `package.json`
3. Перевірте версію Node.js
4. Перевірте, чи правильно налаштовані змінні середовища

### Проблеми з доменом

Якщо виникають проблеми з доменом:

1. Перевірте налаштування DNS
2. Переконайтеся, що CNAME-запис правильно налаштований
3. Перевірте статус SSL/TLS-сертифіката

### Проблеми з продуктивністю

Якщо виникають проблеми з продуктивністю:

1. Перевірте розмір 3D-моделей
2. Оптимізуйте зображення
3. Перевірте кешування
4. Використовуйте інструменти для аналізу продуктивності (Lighthouse, WebPageTest)
