name = "3d-marketplace"
main = "src/index.ts"
compatibility_date = "2024-01-01"

# Durable Objects
[[durable_objects.bindings]]
name = "DOWNLOAD_MANAGER"
class_name = "DownloadManager"
script_name = "3d-marketplace"

# Environment variables
[env.production.vars]
ENVIRONMENT = "production"

[env.staging.vars]
ENVIRONMENT = "staging"

[env.development.vars]
ENVIRONMENT = "development"

# D1 Database bindings
[[env.production.d1_databases]]
binding = "DB"
database_name = "3d-marketplace-enhanced"
database_id = "9dc65021-64f6-48f8-b6c3-bfc817746584"

[[env.production.d1_databases]]
binding = "LEGACY_DB"
database_name = "3d-marketplace-production"
database_id = "5d1e42f6-659c-42b5-83fa-bafbcca86cfd"

[[env.staging.d1_databases]]
binding = "DB"
database_name = "3d-marketplace-staging"
database_id = "bc8fb549-1e21-4652-804c-2b5412d57050"

[[env.development.d1_databases]]
binding = "DB"
database_name = "3d-marketplace-development"
database_id = "a688a285-80b9-4449-b643-20db8030eb06"

# R2 Storage bindings
[[env.production.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-marketplace-models-prod"

[[env.staging.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-marketplace-models-staging"

[[env.development.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-marketplace-models-dev"

# KV Storage bindings (for caching)
[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "768cd42bbee14bce81819a0ef3666930"

[[env.production.kv_namespaces]]
binding = "JOB_QUEUE_KV"
id = "dc0b307ecd034ccd8d44223242adea57"

[[env.staging.kv_namespaces]]
binding = "CACHE_KV"
id = "3d5f7712807c403a93c41f0dfd6401d4"

[[env.development.kv_namespaces]]
binding = "CACHE_KV"
id = "9711cfa19bd04ce4afbd8b28bd051f7b"

# Analytics Engine bindings (for tracking)
[[env.production.analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "3d_marketplace_analytics"

[[env.staging.analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "3d_marketplace_analytics_staging"

[[env.development.analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "3d_marketplace_analytics_dev"

# Queue bindings (for background tasks)
[[env.production.queues]]
binding = "BACKGROUND_QUEUE"
queue = "3d-marketplace-tasks"

[[env.staging.queues]]
binding = "BACKGROUND_QUEUE"
queue = "3d-marketplace-tasks-staging"

[[env.development.queues]]
binding = "BACKGROUND_QUEUE"
queue = "3d-marketplace-tasks-dev"

# Secrets (set via wrangler secret put)
# STRIPE_SECRET_KEY
# STRIPE_WEBHOOK_SECRET
# NEXTAUTH_SECRET
# GITHUB_CLIENT_ID
# GITHUB_CLIENT_SECRET
# GOOGLE_CLIENT_ID
# GOOGLE_CLIENT_SECRET
# EMAIL_SERVER_HOST
# EMAIL_SERVER_PORT
# EMAIL_SERVER_USER
# EMAIL_SERVER_PASSWORD
# EMAIL_FROM

# Build configuration
[build]
command = "npm run build"
cwd = "."

# Compatibility flags
compatibility_flags = ["nodejs_compat"]

# Node.js compatibility
node_compat = true

# Limits
[limits]
cpu_ms = 50000
